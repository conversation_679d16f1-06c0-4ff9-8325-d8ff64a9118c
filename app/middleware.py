from fastapi import <PERSON><PERSON><PERSON>, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from fastapi.responses import JSONResponse
import time
import logging
from typing import List
import uuid
import json
import gzip
from starlette.responses import Response
from fastapi.exceptions import RequestValidationError

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging request/response details."""

    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id

        # Log request
        logger.info(
            f"Request {request_id} started: {request.method} {request.url.path}"
        )

        # Process request and measure time
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # Log response
        logger.info(
            f"Request {request_id} completed: {response.status_code} in {process_time:.4f}s"
        )

        return response


class UserDataMiddleware(BaseHTTPMiddleware):
    """Middleware for extracting user data from headers."""

    async def dispatch(self, request: Request, call_next):
        # Check if x-user-data header exists
        x_user_data = request.headers.get("x-user-data")
        if x_user_data:
            try:
                user_data = json.loads(x_user_data)
                user_id = user_data.get("user_id")
                if user_id:
                    # Add user_id to request state
                    request.state.user_id = user_id
                else:
                    logger.warning("Missing user_id in X-USER-DATA header")
            except json.JSONDecodeError:
                logger.warning("Invalid JSON in X-USER-DATA header")

        return await call_next(request)


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Custom handler for validation errors that returns a simplified, more user-friendly error format
    """
    errors = {}
    for error in exc.errors():
        # Extract field name from location path
        field = error["loc"][-1] if len(error["loc"]) > 1 else error["loc"][0]
        # Create a simplified message
        errors[field] = error["msg"]

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"success": False, "message": "Validation error", "errors": errors},
    )


class ExcludePathsGZipMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, exclude_templates: List[str]):
        super().__init__(app)
        self.exclude_templates = exclude_templates

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        route = request.scope.get("route")
        route_template = getattr(route, "path", "")
        logger.info(f"Route template: {route_template}")
        if route_template in self.exclude_templates:
            return response

        body = [section async for section in response.body_iterator]
        compressed_body = gzip.compress(b"".join(body))

        # Preserve all original headers and add gzip encoding
        headers = dict(response.headers)
        headers["Content-Encoding"] = "gzip"

        # Remove Content-Length header since compressed content has different length
        headers.pop("Content-Length", None)

        return Response(
            content=compressed_body,
            status_code=response.status_code,
            headers=headers,
            media_type=response.media_type,
        )


def setup_middleware(app: FastAPI):
    """Add all middleware to the application."""
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(UserDataMiddleware)
    # Declare the *route templates* you want to exclude
    exclude_templates = ["/ai/agents/{name}/{session_id}/chat/"]
    app.add_middleware(ExcludePathsGZipMiddleware, exclude_templates=exclude_templates)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    # Add more middleware as needed
