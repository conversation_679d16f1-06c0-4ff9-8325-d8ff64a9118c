import json
import numpy as np
import pandas as pd
import duckdb
import uuid
import plotly.express as px
import plotly.graph_objects as go
import plotly
import os
from google.adk.tools import ToolContext
from pathlib import Path
from datetime import datetime
from agents.common.utils.helpers import minimal_plotly_json

root_dir = Path(__file__).resolve().parent.parent.parent.parent


async def think(thought: str, title: str, description: str) -> dict:
    """Tool to brainstorm query strategies or analysis.

    Args:
        thought (str): Your thoughts about the analysis strategy.
        title (str): Title for the thought.
        description (str): Description of the thought in 25 words.

    Returns:
        dict: Status and content of the thinking process.
    """
    return {"status": "success", "content": "Tool ran without errors or output."}


async def sql(
    query: str, title: str, description: str, tool_context: ToolContext
) -> dict:
    """Run a SQL query on DuckDB and return artifact-ready output.

    Args:
        query (str): The SQL query to execute.
        title (str): Title for the artifact to display.
        description (str): Description of the query's goal in 25 words.

    Returns:
        dict: Query results with status, content, title, description and artifact ID.
    """
    try:
        request_data = tool_context.state.get("additional_request_data")
        DATABASE_URL = str(
            root_dir
            / f"data/{request_data['organization']}/{request_data['module_name']}/grs_archo_full.db"
        )
        con = duckdb.connect(database=DATABASE_URL, read_only=True)
        df = con.sql(query).df()
        summary_df = con.sql(f"SUMMARIZE {query}").df()
        con.close()
    except Exception as e:
        return {"status": "error", "content": f"SQL ERROR: {e}\n Try again."}

    content = ""

    if len(df) > 100:
        content = f"""Artifact displayed to user. Results truncated (over 100 rows).
<records>
{df}
</records>
<summary>
{summary_df}
</summary>
"""
    if df.empty:
        content = "The query returned no results."

    content = f"""Artifact displayed to user.
<records>
{df}
</records>
"""

    # Generate a UUID for the artifact
    artifact_id = uuid.uuid4().hex

    # Create the artifact directory
    artifact_dir = root_dir / f"data/artifacts/{artifact_id}"
    os.makedirs(artifact_dir, exist_ok=True)

    # Create metadata.json
    metadata = {
        "type": "table",
        "title": title,
        "description": description,
        "created_at": datetime.now().isoformat(),
    }

    # Save metadata.json
    with open(artifact_dir / "metadata.json", "w") as f:
        json.dump(metadata, f, indent=2)

    # Save table_data.csv
    df.to_csv(artifact_dir / "table_data.csv", index=False)

    return {
        "status": "success",
        "content": content,
        "artifact_id": artifact_id,
        "artifact_type": "table",
        "title": title,
        "description": description,
    }


async def display_chart(
    chart_code: str, title: str, description: str, query: str, tool_context: ToolContext
) -> dict:
    """Display data to the user as an artifact. Use this only after running a query to confirm the data is what you want to display. Please try to keep the chart simple and readable.
    # Known issues:
    - Addition/subtraction of integers and integer-arrays with Timestamp is no longer supported.  Instead of adding/subtracting n, use n * obj.freq
    - Plotly Express cannot process wide-form data with columns of different type.

    Args:
        chart_code (str): The code to display the artifact as a chart. This code will be run inline in a jupyter notebook cell. These imports are already available: import pandas as pd, import plotly, import plotly.express as px, import plotly.graph_objects as go, import numpy as np, import json. Use Plotly Express to create the chart. The data is already in a pandas DataFrame variable named df. Plotly code must be saved to a variable named fig. Focus on making readable charts. Carefully consider the best type of chart to use for the data. Use comments in the code to explain your reasoning. Start the code by planning your thoughts in comments. Make the chart simple and readable. Do not make it too complex. Do not set explicit width and height for the figure. Let plotly choose the best size. Try not to set explicit colors when you can use plotly's defaults like for backgrounds. A theme is already applied. use make_subplots to create multiple charts in one figure.
        title (str): Title of the chart.
        description (str): Goal of the chart in 25 words.
        query (str): Query that returned the data to display.

    Returns:
        dict: Status, content, title, description and artifact ID.
    """
    try:
        # Connect to DuckDB and execute the query
        request_data = tool_context.state.get("additional_request_data")
        DATABASE_URL = str(
            root_dir
            / f"data/{request_data['organization']}/{request_data['module_name']}/grs_archo_full.db"
        )
        con = duckdb.connect(database=DATABASE_URL, read_only=True)
        df = con.sql(query).df()
        con.close()
    except Exception as e:
        return f"SQL ERROR: {e}\nTry again."

    if df.empty:
        return "The query returned no results to display."

    try:
        # Make 'df' available for execution context
        global_vars = {
            "df": df,
            "px": px,
            "go": go,
            "pd": pd,
            "np": np,
            "plotly": plotly,
            "json": json,
        }
        # Evaluate the user-provided chart code
        exec(chart_code, global_vars)
        fig = global_vars.get("fig")
        if fig is None:
            return "No figure ('fig') was created in the chart code."

        # Set the title from the input parameter
        fig.update_layout(title_text=title)

        # Generate a UUID for the artifact
        artifact_id = uuid.uuid4().hex

        # Create the artifact directory
        artifact_dir = root_dir / f"data/artifacts/{artifact_id}"
        os.makedirs(artifact_dir, exist_ok=True)

        # Create metadata.json
        metadata = {
            "type": "chart",
            "title": title,
            "description": description,
            "created_at": datetime.now().isoformat(),
        }
        chart_json = minimal_plotly_json(fig)
        # Save metadata.json
        with open(artifact_dir / "metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)

        # Save chart_config.json
        with open(artifact_dir / "chart_config.json", "w") as f:
            json.dump(chart_json, f, indent=2)

        return {
            "status": "success",
            "content": "Chart displayed successfully.",
            "title": title,
            "description": description,
            "artifact_id": artifact_id,
            "artifact_type": "chart",
        }

    except Exception as e:
        return {
            "status": "error",
            "content": f"CHART EXECUTION ERROR: {e}\nCheck the chart_code syntax and try again.",
        }


async def get_all_layers(
    title: str, description: str, tool_context: ToolContext
) -> dict:
    """Fetches all the layers that are displayed in the application.

    Args:
        title (str): Title for fetching layers.
        description (str): Description of the goal in 15 to 20 words.

    Returns:
        dict: Status
    """
    return {"status": "pending"}


async def toggle_layers(
    title: str,
    description: str,
    visibility: str,
    layers: list[str],
    tool_context: ToolContext,
) -> dict:
    """Open a form in the application only when you have the available forms.

    Args:
        title (str): Title for toggling layers.
        description (str): Description of the  goal in 25 words.
        visibility (str): Visibility of the layers. Can be 'show' or 'hide'.
        layers (list[str]): List of layers to toggle after having all the available layers


    Returns:
        dict: Status
    """
    return {"status": "pending"}
