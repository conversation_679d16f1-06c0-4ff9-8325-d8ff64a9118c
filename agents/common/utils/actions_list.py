"""
Action names for the client to perform an action and continue the conversation
"""

# Terra module actions
TERRA_ACTIONS = [
    "get_all_layers",
    "toggle_layers",
    "get_visible_layers",
    "get_classes_breakdown",
    "get_filters",
    "set_filters",
    "update_classes",
]

# PM (Project Management) module actions
PM_ACTIONS = []

# Forms module actions
FORMS_ACTIONS = [
    "open_form",
    "fetch_all_forms",
]

# Combined list of all action names from all modules
ALL_ACTIONS = TERRA_ACTIONS + PM_ACTIONS + FORMS_ACTIONS


def is_valid_action(action_name: str) -> bool:
    """
    Check if the given action name is valid (exists in any module).

    Args:
        action_name: The name of the action to validate

    Returns:
        bool: True if action is valid, False otherwise
    """
    return action_name in ALL_ACTIONS


def get_all_actions() -> list[str]:
    """
    Get all available action names.

    Returns:
        list[str]: List of all action names from all modules
    """
    return ALL_ACTIONS.copy()


def get_actions_by_module() -> dict[str, list[str]]:
    """
    Get actions organized by module.

    Returns:
        dict: Dictionary with module names as keys and action lists as values
    """
    return {
        "terra": TERRA_ACTIONS.copy(),
        "pm": PM_ACTIONS.copy(),
        "forms": FORMS_ACTIONS.copy(),
    }
